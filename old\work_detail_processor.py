# -*- coding: utf-8 -*-
# @Time : 2025/6/11 16:52
# <AUTHOR> cyf
# @File : work_detail_processor.py

import json
from typing import Dict, List, Optional
from xmlrpc.client import Boolean

from client.bitable.dingtalk_bitable_factory import DingtalkBitableFactory
from client.bitable.dingtalk_bitable_notifier import DingTalkBitableNotifier
from client.service_client import Environment
from config.xhs_article_config import updater_dict, article_task_dict
from dao.model.author_work import AuthorWork
from dao.repository.work_detail_task_notify_record_repository import WorkDetailTaskNotifyRecordRepository
from scene.data_sync import DingTalkDataSync
from scene.dingtalk_alerter import DingTalkAlerter
from scene.model.work_detail_task_model import WorkDetailTaskModel
from scene.xhs_detail_updater import XhsDetailUpdater
from utils.string_utils import StringUtils
from utils.time_utils import TimeUtils


class WorkDetailProcessor:
    """
    小红书处理流程
    """

    def __init__(self, env: Environment, logger, company):
        self.logger_ = logger
        self.env = env

        self.task_notify_record_repo = WorkDetailTaskNotifyRecordRepository()
        self.dingtalk_notifier = DingtalkBitableFactory.create_object(company, logger, company)
        self.ding_alerter = DingTalkAlerter()

    def start_job(self):
        for _, v in updater_dict.items():
            self.logger_.info(f"start company :{_}")
            task_config = article_task_dict.get(v[0], {})

            if not self.__check_active(task_config):
                self.logger_.info(f"公司：{_}, 配置没有开启: {v}")
                continue

            __webhook = task_config.get("bitable", {}).get("webhook")
            if not __webhook:
                self.logger_.info(f"公司：{_}, 配置没有开启: {v}")
                continue

            user_info = task_config.get("user_info", {})
            self.__process(
                user_info=user_info,
                updater_name=task_config.get("company"),
                bitable=task_config.get("bitable"),
            )

    async def start_choice_job(self, company: str):
        task_config_arr: List = updater_dict.get(company)
        task_config: dict = article_task_dict.get(task_config_arr[0])

        # check active
        if not self.__check_active(task_config):
            self.logger_.info(f"公司：{company}, 配置没有开启")

        # check webhook
        __webhook = task_config.get("bitable", {}).get("webhook")
        if not __webhook:
            self.logger_.info(f"公司：{company}, webhook 未配置")

        user_info = task_config.get("user_info", {})
        await self.__process(
            user_info=user_info,
            updater_name=task_config.get("company"),
            bitable=task_config.get("bitable"),
        )

    def __check_active(self, task_config: Dict) -> Boolean:
        return task_config.get("is_active", False)

    async def __process(self, user_info: Dict, updater_name: str, bitable: dict):
        updater = self._generate_updater(user_info=user_info, updater_name=updater_name)

        # 获取需要更新的数据， 一次更新一个业务
        work_list: List[WorkDetailTaskModel] = await updater.run()
        if not work_list:
            self.logger_.info(f"{user_info}, 没有需要更新的数据")
            return
        self.logger_.info(f"获取到的作品列表为 : {work_list}")

        # 发送 webhook
        _webhook = bitable.get("webhook")
        _dentry_uuid = bitable.get("dentryUuid")
        _id_or_name = bitable.get("idOrName")

        # 水云兔批量更新
        if updater_name in ["水云兔", "淘天", "告趣","云鸥"]:
            self.logger_.info("==== 水云兔 批量提交 ====")

            self._process_work_list(work_list, 100, user_info, _webhook, updater_name)

            # 处理失败的链接
            self.__send_fail_msg(updater.covert_fail_queue, updater_name)
            return

        # 其他平台
        data_sync = DingTalkDataSync(self.logger_, _webhook, _dentry_uuid, _id_or_name)

        webhook_data = self.__update_webhook_formatter(
            work_list, _dentry_uuid, _id_or_name, updater_name
        )
        data_sync.constom_invoke(json_data=webhook_data)

    def __notify_submit_user(self, user_id, work: WorkDetailTaskModel, submit_user_list, company):
        try:
            work.threshold = "否"
            _work_detail: AuthorWork = work.author_work

            if not submit_user_list or len(submit_user_list) == 0:
                self.logger_.info(f"notify user is null {user_id}")
                return

            is_over_threshold, msg = self._check_is_threshold(company=company, work=work)
            # like_count = _work_detail.like_count
            # if like_count >= 50:
            if is_over_threshold:
                work_id = work.work_id
                work.threshold = "是"

                # if self.task_notify_record_repo.has_record(user_id, work_id):
                #     return

                self.task_notify_record_repo.insert(user_id,
                                                    "",
                                                    json.dumps(submit_user_list),
                                                    work_id, work.platform)

                for submit_user in submit_user_list:
                    try:
                        union_id = submit_user.get("unionId")
                        self.logger_.info(f"start notify union_id: {union_id} and msg: {msg}")
                        self.dingtalk_notifier.send_message(union_id, msg)
                    except Exception as ee:
                        self.logger_.error(f"{ee}")

        except Exception as e:
            self.logger_.error(f"{e}")

    def _check_is_threshold(self, company, work: WorkDetailTaskModel):
        _author_work = work.author_work

        if company == "水云兔":
            like_count = _author_work.like_count
            if like_count >= 50:
                return True, f"作品阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 作品链接={work.work_url}"
            return False, ""
        elif company == "淘天":
            threshold = 50
            if _author_work.like_count > threshold:
                return True, f"1688舆情小助手-风险发酵提醒：工单编号{work.extends}，点赞数已达到{threshold}，请及时处理，风险链接：{work.work_url} "
            if _author_work.comment_count > threshold:
                return True, f"1688舆情小助手-风险发酵提醒：工单编号{work.extends}，评论数已达到{threshold}，请及时处理，风险链接：{work.work_url} "
            if _author_work.share_count > threshold:
                return True, f"1688舆情小助手-风险发酵提醒：工单编号{work.extends}，分享数已达到{threshold}，请及时处理，风险链接：{work.work_url} "
            if _author_work.collect_count > threshold:
                return True, f"1688舆情小助手-风险发酵提醒：工单编号{work.extends}，收藏数已达到{threshold}，请及时处理，风险链接：{work.work_url} "
        elif company == "告趣":
            threshold = 200
            if _author_work.like_count > threshold:
                return True, f"告趣小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 点赞数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.comment_count > threshold:
                return True,f"告趣小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 评论数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.share_count > threshold:
                return True, f"告趣小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 分享数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.collect_count > threshold:
                return True, f"告趣小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 收藏数已达到{threshold}，作品链接={work.work_url}"
        elif company == "云鸥":
            threshold = 50
            if _author_work.like_count > threshold:
                return True, f"云鸥小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 点赞数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.comment_count > threshold:
                return True,f"云鸥小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 评论数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.share_count > threshold:
                return True, f"云鸥小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 分享数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.collect_count > threshold:
                return True, f"云鸥小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 收藏数已达到{threshold}，作品链接={work.work_url}"
        return False, ""

    def _process_work_list(
            self,
            work_list: list[WorkDetailTaskModel],
            batch_size: int,
            user_info,
            _webhook,
            updater_name
    ) -> None:
        """
        分批处理字典列表的通用函数

        参数:
        work_list: 需要处理的字典列表
        batch_size: 每批处理的数量，默认为100
        """
        if not work_list:
            self.logger_.info("⚠️ 工作列表为空，无需处理")
            return

        total_items = len(work_list)
        num_batches = (total_items + batch_size - 1) // batch_size
        for batch_index in range(num_batches):
            # 计算当前批次的起止索引
            start_index = batch_index * batch_size
            end_index = min((batch_index + 1) * batch_size, total_items)

            # 获取当前批次数据
            current_batch = work_list[start_index:end_index]
            try:
                self.__send_sync_batch(user_info, current_batch, _webhook, updater_name)
            except Exception as e:
                self.logger_.error(f"❌ 第{batch_index + 1}批处理失败: {str(e)}")

    def __send_sync_batch(self,
                          user_info: Dict,
                          work_list: List[WorkDetailTaskModel],
                          _webhook: str,
                          updater_name: str):
        user_id = user_info.get("user_id")

        grouped_works = {}
        for work in work_list:
            dentry_uuid = work.dentry_uuid
            submit_users = work.submit_user

            self.__notify_submit_user(user_id, work, submit_users, updater_name)

            if not dentry_uuid:
                self.logger_.info(f"该条记录没有携带表信息: {work}")
                continue
            if dentry_uuid not in grouped_works:
                grouped_works[dentry_uuid] = []

            grouped_works[dentry_uuid].append(work)
        self.logger_.info(f"group by dentry_uuid : {StringUtils.obj_2_json_string(grouped_works)}")

        for _dentry_uuid, works in grouped_works.items():
            if not works:
                continue

            _id_or_name = works[0].id_or_name

            self.logger_.info(f"start sync bit table {_dentry_uuid}")

            try:
                webhook_data = self.__update_webhook_formatter(
                    works, _dentry_uuid, _id_or_name, updater_name
                )

                self.logger_.info(f"webhook params :{webhook_data}")
                data_sync = DingTalkDataSync(
                    self.logger_, _webhook, _dentry_uuid, _id_or_name
                )
                data_sync.constom_invoke(json_data=webhook_data)
            except Exception as e:
                self.logger_.error(f"同步数据到钉钉失败 {_dentry_uuid}: {e}")

    def _generate_updater(self, user_info: Dict, updater_name: str) -> XhsDetailUpdater:
        """
        "user_info": {
            "api-token": "api-token-xxx",
            "user_id": 9
        }
        """
        if not user_info or not user_info.get("api-token", ""):
            raise Exception("配置错误")

        updater: XhsDetailUpdater = XhsDetailUpdater(
            environment=self.env,
            logger_=self.logger_,
            updater_name=updater_name,
            user_info=user_info,
        )
        return updater

    def __update_webhook_formatter(
            self, work_list: List[WorkDetailTaskModel], dentry_uuid: str, id_or_name: str, updater: str
    ) -> Optional[Dict]:

        self.logger_.info(f"start build params :{updater}")
        """
            水云兔/优尔佳佳/ 朱栈科技
        """
        try:
            if updater == "水云兔":
                return self.__covert_syt(work_list, dentry_uuid, id_or_name)
            elif updater == "佳尔优优":
                return self.__covert_jyy(work_list, dentry_uuid, id_or_name)
            elif updater == "朱栈科技":
                return self.__covert_zz(work_list)
            elif updater == "淘天":
                return self.__covert_syt(work_list, dentry_uuid, id_or_name)
            elif updater == "告趣":
                return self.__covert_gq(work_list, dentry_uuid, id_or_name)
            elif updater == "云鸥":
                return self.__covert_gq(work_list, dentry_uuid, id_or_name)
        except Exception as e:
            self.logger_.error(f"构建 webhook 参数失败: {e}")
            return None

    def __send_fail_msg(self, fail_queue: list, updater_name: str):
        if not fail_queue:
            self.logger_.info("无失败的链接")
            return
        msg = f"{updater_name}:处理失败的数据: \n {json.dumps(fail_queue, ensure_ascii=False, indent=2)}"
        self.ding_alerter.send(msg=msg)

    def __covert_zz(self, work_list: List[WorkDetailTaskModel]) -> Optional[Dict]:
        """
        朱栈科技
        :param work_list:
        :return:
        """
        webhook_params: List[Dict] = []
        for _work in work_list:
            _author_work: AuthorWork = _work.author_work
            result = {
                "rowId": _work.row_id,
                "workUrl": _work.work_url,
                "likeCount": _author_work.like_count,
                "commentCount": _author_work.comment_count,
                "shareCount": _author_work.share_count,
                "collectCount": _author_work.collect_count,
            }
            webhook_params.append(result)
        json_data = {"data": webhook_params}
        return json_data

    def __covert_syt(self, work_list: List[WorkDetailTaskModel], dentry_uuid: str, id_or_name: str) -> Optional[Dict]:
        """
        水云兔
        :param work_list:
        :return:
        """
        update_records = []
        for work in work_list:
            _author_work: AuthorWork = work.author_work

            _platform_name = "抖音"
            if _author_work.platform == "xhs":
                _platform_name = "小红书"
            elif _author_work.platform == "dy":
                _platform_name = "抖音"

            result = {
                "作品标题": _author_work.title,
                "平台": _platform_name,
                "作者": _author_work.author_name,
                "作者主页": _author_work.author_url,
                "正文": _author_work.content,
                "作品发布时间": _author_work.publish_time,
                "点赞数": _author_work.like_count,
                "评论数": _author_work.comment_count,
                "分享数": _author_work.share_count,
                "收藏数": _author_work.collect_count,
                "更新次数": work.update_count,
                "是否触发阈值": work.threshold,
                "更新时间": TimeUtils.get_current_ts(),
            }
            update_records.append({
                "id": work.row_id,
                "fields": result
            })
        json_data = {
            "dentryUuid": dentry_uuid,
            "idOrName": id_or_name,
            "updateRecords": update_records,
        }
        return json_data

    def __covert_gq(self, work_list: List[WorkDetailTaskModel], dentry_uuid: str, id_or_name: str) -> Optional[Dict]:

        """
        告趣
        :param work_list:
        :return:
        """
        update_records = []
        for work in work_list:
            if not work.is_exist:
                result = {
                    "更新次数": work.update_count,
                    "作品是否可访问": "否",
                    "更新时间" : TimeUtils.get_current_ts(),
                }
            else:
                _author_work: AuthorWork = work.author_work

                _platform_name = "抖音"
                if _author_work.platform == "xhs":
                    _platform_name = "小红书"
                elif _author_work.platform == "dy":
                    _platform_name = "抖音"

                result = {
                    "作品标题": _author_work.title,
                    "平台": _platform_name,
                    "作者": _author_work.author_name,
                    "作者主页": _author_work.author_url,
                    "正文": _author_work.content,
                    "作品发布时间": _author_work.publish_time,
                    "点赞数": _author_work.like_count,
                    "评论数": _author_work.comment_count,
                    "分享数": _author_work.share_count,
                    "收藏数": _author_work.collect_count,
                    "更新次数": work.update_count,
                    "是否触发阈值": work.threshold,
                    "更新时间": TimeUtils.get_current_ts(),
                    "作品是否可访问": "是",
                }
            update_records.append({
                "id": work.row_id,
                "fields": result
            })
        json_data = {
            "dentryUuid": dentry_uuid,
            "idOrName": id_or_name,
            "updateRecords": update_records,
        }
        return json_data

    def __covert_jyy(self, work_list: List[WorkDetailTaskModel], dentry_uuid: str, id_or_name: str) -> Optional[Dict]:
        """
        佳尔优优
        :param work_list:
        :param dentry_uuid:
        :param id_or_name:
        :return:
        """
        update_records = []
        for work in work_list:
            _author_work: AuthorWork = work.author_work
            result = {
                "标题": _author_work.title,
                "正文": _author_work.content,
                "作者": _author_work.author_name,
                "作者主页": _author_work.author_url,
                "发布时间": _author_work.publish_time,
                "点赞数": _author_work.like_count,
                "评论数": _author_work.comment_count,
                "分享数": _author_work.share_count,
                "收藏数": _author_work.collect_count,
                "更新时间": TimeUtils.get_current_ts(),
            }
            update_records.append({"id": work.row_id, "fields": result})

        json_data = {
            "dentryUuid": dentry_uuid,
            "idOrName": id_or_name,
            "updateRecords": update_records,
        }
        return json_data


if __name__ == "__main__":
    # process = WorkDetailProcessor()
    # process.start_job()
    di = {'dentryUuid': 'QG53mjyd80xQ1wLDSdkP7GGa86zbX04v', 'idOrName': '基础数据表', 'updateRecords': [{'id': 'ML6NkbVmPY', 'fields': {'作品标题': '男友剃须新宠！纽立让他从粗糙刮到精致型男', '平台': '小红书', '作者': '涂涂小兔', '作者主页': 'https://www.xiaohongshu.com/user/profile/67bb118e00000000040312d8', '正文': '\n  每次给男朋友选礼物都纠结到挠头，鞋子怕尺码不对，数码产品怕踩雷，直到遇见纽立剃须刀——直接被他夸成"宝藏礼物"！现在他刮完胡子就凑过来求表扬："看这效果，你买的剃须刀也太牛了！" 他以前总说下巴和人中的顽固胡茬难剃，现在纽立的刀头完美贴合脸部轮廓，一推即净。哪怕是硬茬丛生的络腮胡，在35000转/分钟强劲马达的驱动下也能轻松剃除。 他最烦剃须刀续航差，而这款快充1小时就能续航90天，出差旅行随手塞包就走。磁吸式刀头保护盖设计超实用，一扣即合超牢固，再也不用担心盖子丢失！ 从以前草草了事到现在享受剃须时光，看他每天精致清爽出门，终于明白——选对剃须刀真的能提升生活品质！送男友礼物纠结症患者，闭眼入这款，实用又走心，他绝对爱不释手！ #纽立剃须刀[话题]# #剃须刀推荐[话题]# #剃须刀测评[话题]# #男士理容[话题]# #纽立剃须刀[话题]# #国货剃须[话题]# #胡须粗硬[话题]# #剃须刀[话题]# #剃须用品[话题]# #剃须护理[话题]# #上班族推荐[话题]# #男士必备[话题]# #国货剃须刀[话题]# #好用剃须刀[话题]# #七夕节几月几号[话题]##七夕送男朋友礼物[话题]##七夕节礼物[话题]##成长品牌夏学季[话题]##小红书种草学[话题]##种草学[话题]# \n', '作品发布时间': '2025-07-05 17:44:13', '点赞数': 8, '评论数': 2, '分享数': 1, '收藏数': 4, '更新次数': 5, '是否触发阈值': '否', '更新时间': '2025-07-07 14:22:22'}}]}
    dic = json.dumps(di, ensure_ascii=False, indent=2)
    print(dic)
    pass
