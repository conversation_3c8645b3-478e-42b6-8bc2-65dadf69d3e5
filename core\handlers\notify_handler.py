"""
通知逻辑Handler - 负责基于阈值规则发送通知
"""

from config.client_config import ClientConfig
from dao.repository.work_detail_task_notify_record_repository import WorkDetailTaskNotifyRecordRepository
from .base_handler import BaseHandler
from ..exceptions import NotificationException
from ..pipeline_context import PipelineContext


class NotifyHandler(BaseHandler):
    """
    通知逻辑处理器基类

    职责：
    - 检查作品是否达到通知阈值
    - 发送通知给相关人员
    - 记录通知历史
    """

    def __init__(self, client_config: ClientConfig, logger_):
        super().__init__(client_config, logger_)
        self.logger_ = logger_
        self.task_notify_record_repo = WorkDetailTaskNotifyRecordRepository()


    def handle(self, context: PipelineContext) -> PipelineContext:
        """
        处理通知逻辑

        Args:
            context: Pipeline上下文

        Returns:
            更新后的Pipeline上下文
        """
        try:
            self.logger.info(f"Starting notification process for {len(context.task_list)} works")

            notification_results = []
            notifications_sent = 0

            for work in context.task_list:
                try:
                    result = self._process_work_notification(work, context)
                    notification_results.append(result)

                    if result.get("sent", False):
                        notifications_sent += 1

                except Exception as e:
                    self.logger.error(f"Failed to process notification for {work.work_id}: {e}")
                    notification_results.append(
                        {"work_id": work.work_id, "success": False, "sent": False, "error": str(e)}
                    )


            self.logger.info(
                f"Notification process completed: {notifications_sent} notifications sent"
            )

            # 传递给下一个处理器
            return self.handle_next(context)

        except Exception as e:
            self.logger.error(f"Notification process failed: {e}")
            raise NotificationException(f"Failed to process notifications: {str(e)}")
