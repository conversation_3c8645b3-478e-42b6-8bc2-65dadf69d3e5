"""
更新逻辑Handler - 负责调用平台API获取最新的作品详情数据
"""
import json
import logging
import time
from typing import List, Optional
from abc import ABC, abstractmethod

from dao.model.author_work import AuthorWork
from dao.model.work_detail_refresh import WorkDetailRefreshRecord
from dao.repository.xhs_work_detail_refresh_repository import XhsWorkDetailRefreshRepository
from jss_api_extend import DouyinDetailHandler, XhsDetailHandler
from utils.string_utils import StringUtils
from utils.time_utils import TimeUtils

from .base_handler import BaseHandler
from config.client_config import ClientConfig
from ..pipeline_context import PipelineContext
from ..exceptions import WorkUpdateException
from models.work_models import WorkDetailTaskModel


class UpdateHandler(BaseHandler):
    """
    更新逻辑处理器基类

    职责：
    - 调用各平台API获取作品最新数据
    - 更新作品详情信息
    - 处理批量更新逻辑
    """

    def __init__(self, client_config: ClientConfig, logger_):
        super().__init__(client_config, logger_)
        self.platform_handlers = {}
        self.logger_ = logger_
        self.dy_handler = DouyinDetailHandler(logger_)
        self.xhs_handler = XhsDetailHandler(logger_)
        self.refresh_repository = XhsWorkDetailRefreshRepository()

    def handle(self, context: PipelineContext) -> PipelineContext:
        """
        处理更新逻辑

        Args:
            context: Pipeline上下文

        Returns:
            更新后的Pipeline上下文
        """
        try:
            self.logger.info(f"Starting update process for {len(context.task_list)} tasks")


            # 批量处理任务
            updated_tasks = self._process_tasks_batch(context.task_list, context)

            # 设置到上下文
            context.task_list = updated_tasks
            context.successful_updates = len(
                [t for t in updated_tasks if t.author_work is not None]
            )
            context.failed_updates = len(updated_tasks) - context.successful_updates

            self.logger.info(
                f"Update completed: {context.successful_updates} successful, {context.failed_updates} failed"
            )

            # 传递给下一个处理器
            return self.handle_next(context)

        except Exception as e:
            self.logger.error(f"Update process failed: {e}")
            raise WorkUpdateException(f"Failed to update works: {str(e)}")

    async def run(self) -> Optional[List[WorkDetailTaskModel]]:
        try:

            detail_list: List[WorkDetailTaskModel] = []

            for task in task_list:
                try:
                    result: WorkDetailTaskModel = await self._process_task(task_model=task, record_id=record_id)
                    if result:
                        self.logger_.info(f"success handle work :{StringUtils.obj_2_json_string(result)}")

                        update_count = int(task.update_count)
                        update_count += 1
                        task.update_count = str(update_count)

                        detail_list.append(result)

                except Exception as e:
                    self.logger_.error(f"处理任务失败: {task}, 错误: {e}")
                    continue

            self.logger_.info(f"更新流程完成，成功处理 {len(detail_list)} 个任务")

            self.logger_.info(
                f"更新失败的链接 userId:{self.user_id},{json.dumps(self.covert_fail_queue, ensure_ascii=False, indent=2)}")
            return detail_list
        except Exception as e:
            self.logger.error(f"<UNK>: {e}")

    async def _process_task(self,
                            task_model: WorkDetailTaskModel,
                            batch_id: str) \
            -> Optional[WorkDetailTaskModel]:
        """
        处理单个任务
        """

        try:
            global _work_detail

            _id = task_model.id
            _platform = task_model.platform
            _work_id = task_model.work_id

            self.logger_.info(f"开始处理任务: work_url :{task_model.work_url}")

            if _platform == "dy":
                _work_detail, is_exist = await self.dy_handler.query_article_detail(_work_id)
            elif _platform == "xhs":
                _work_detail, is_exist = self.xhs_handler.query_article_detail(_work_id)
            else:
                self.logger_.error(f"未知的平台: {_platform}")

                return None

            if not _work_detail and is_exist is None:
                self.logger_.error(f"获取作品详情失败 API 接口失败: work_url={task_model.work_url}")


                return None
            # 详情
            task_model.author_work = _work_detail
            task_model.is_exist = is_exist

            if not is_exist:
                return task_model

            __db_record = self._covert_author_work_to_db_record(
                record_id=record_id,
                origin_work_url=task_model.work_url,
                platform=_platform,
                author_work=_work_detail
            )
            self.logger_.info(f"成功获取作品详情: work_id={_work_id}")
            record_id_db = self.refresh_repository.insert_refresh_record(__db_record)

            self.refresh_repository.update_refresh_task_status(record_id=record_id, work_id=_work_id, status=1)
            self.logger_.info(f" == 更新任务状态成功 == record_id：{record_id}, work_id:{_work_id}")

            self.logger_.info(f"任务处理完成: {StringUtils.obj_2_json_string(task_model)}")
            return task_model

        except Exception as e:
            self.logger_.error(f"处理任务异常: {StringUtils.obj_2_json_string(task_model)}, 错误: {e}")
            return None

    def _covert_author_work_to_db_record(
            self,
            record_id: str,
            origin_work_url: str,
            platform: str,
            author_work: AuthorWork
    ) -> Optional[WorkDetailRefreshRecord]:
        """
            构建记录
        """
        current_time = TimeUtils.get_current_ts()

        return WorkDetailRefreshRecord(
            id=None,
            record_id=record_id,
            platform=platform,
            author_id=author_work.author_id,
            author_identity=author_work.author_identity,
            author_avatar=author_work.author_avatar,
            author_name=author_work.author_name,
            author_url=author_work.author_url,
            work_id=author_work.work_id,
            work_uuid=author_work.work_uuid,
            url=author_work.url,
            download_url=author_work.download_url,
            long_url=origin_work_url,
            digest=author_work.digest,
            title=author_work.title,
            thumbnail_link=author_work.thumbnail_link,
            content=author_work.content,
            img_urls=author_work.img_urls,
            video_urls=author_work.video_urls,
            music_url=author_work.music_url,
            music_author_name=author_work.music_author_name,
            music_id=author_work.music_id,
            music_name=author_work.music_name,
            publish_time=author_work.publish_time,
            publish_day=author_work.publish_day,
            location_ip=author_work.location_ip,
            read_count=author_work.read_count,
            like_count=author_work.like_count,
            comment_count=author_work.comment_count,
            share_count=author_work.share_count,
            collect_count=author_work.collect_count,
            record_time=current_time,
            is_del=0,
        )
