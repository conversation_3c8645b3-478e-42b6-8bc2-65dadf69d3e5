"""
同步数据Handler - 负责将更新后的数据同步回数据源
"""

import logging
import time
from typing import Dict, Any

from config.client_config import ClientConfig
from models.work_models import WorkDetailTaskModel
from .base_handler import BaseHandler
from ..exceptions import DataSyncException
from ..pipeline_context import PipelineContext


class DingTalkSyncHandler(BaseHandler):
    """
    钉钉多维表格同步处理器
    """

    def __init__(self, client_config: ClientConfig, logger_):
        super().__init__(client_config, logger_)
        self._bitable_client = None
        self.logger_ = logger_

    def _perform_sync(
            self, work: WorkDetailTaskModel, mapped_data: Dict[str, Any], context: PipelineContext
    ) -> bool:
        """执行钉钉同步"""
        try:
            bitable_client = context.notifier

            # 更新记录
            result = bitable_client.update_records(work.row_id, mapped_data)

            if result:
                self.logger.debug(f"Successfully synced work {work.work_id} to DingTalk")
                return True
            else:
                self.logger.warning(f"Failed to sync work {work.work_id} to DingTalk")
                return False

        except Exception as e:
            self.logger.error(f"Failed to sync to DingTalk: {e}")
            return False

    def _get_bitable_client(self):
        """获取钉钉多维表格客户端"""
        if self._bitable_client is None:
            try:
                from jss_api_extend.dingtalk_bitable_factory import DingtalkBitableFactory

                bitable_config = self.client_config.bitable_config
                if not bitable_config:
                    raise DataSyncException("Missing bitable configuration")

                self._bitable_client = DingtalkBitableFactory.create_bitable_client(
                    api_token=self.client_config.user_info.get("api-token"),
                    dentry_uuid=bitable_config.get("dentryUuid"),
                    id_or_name=bitable_config.get("idOrName"),
                )

            except ImportError as e:
                self.logger.error(f"Failed to import DingTalk dependencies: {e}")
                raise DataSyncException("DingTalk dependencies not available")
            except Exception as e:
                self.logger.error(f"Failed to create DingTalk client: {e}")
                raise DataSyncException(f"DingTalk client creation failed: {str(e)}")

        return self._bitable_client


class WebhookSyncHandler(BaseHandler):
    """
    Webhook同步处理器
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)

    def _perform_sync(
            self, work: WorkDetailTaskModel, mapped_data: Dict[str, Any], context: PipelineContext
    ) -> bool:
        """执行Webhook同步"""
        try:
            webhook_url = self._get_webhook_url()
            if not webhook_url:
                self.logger.warning("No webhook URL configured")
                return False

            # 构建webhook数据
            webhook_data = {
                "work_id": work.work_id,
                "work_url": work.work_url,
                "platform": work.platform,
                "row_id": work.row_id,
                "batch_id": context.batch_id,
                "client_name": context.client_name,
                "mapped_data": mapped_data,
                "timestamp": time.time(),
            }

            # 发送webhook请求
            import requests

            response = requests.post(
                webhook_url,
                json=webhook_data,
                timeout=30,
                headers={"Content-Type": "application/json"},
            )

            if response.status_code == 200:
                self.logger.debug(f"Successfully sent webhook for work {work.work_id}")
                return True
            else:
                self.logger.warning(
                    f"Webhook returned status {response.status_code} for work {work.work_id}"
                )
                return False

        except Exception as e:
            self.logger.error(f"Failed to send webhook: {e}")
            return False
