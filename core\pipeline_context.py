"""
Pipeline上下文 - 用于在责任链中传递数据和状态
"""

import time
from dataclasses import dataclass, field
from typing import List, Any, Dict, Optional

from jss_api_extend.bitable.base_dingtalk_bitable_notifier import BaseDingTalkBitableNotifier
from models.work_models import WorkDetailTaskModel
from .context import ClientConfig


@dataclass
class PipelineContext:
    # 基本信息
    client_name: str

    # 每次运行的 batch_id
    batch_id: str

    # 配置
    client_config: ClientConfig


    data_source_type: str = "bitable"

    # 错误 url 格式链接
    error_format_url_list: List[Any] = field(default_factory=list)

    # 任务列表
    task_list: List[WorkDetailTaskModel] = field(default_factory=list)

    # 成功更新条数
    successful_updates: int = 0
    # 失败更新条数
    failed_updates: int = 0

    # 更新失败的链接
    failed_update_url_list: List[Any] = field(default_factory=list)

    # 错误记录
    errors: List[str] = field(default_factory=list)

    def add_error(self, error_message: str) -> None:
        """添加错误信息"""
        self.errors.append(error_message)

    def to_summary(self) -> Dict[str, Any]:
        """生成执行摘要"""
        return {
            "client_name": self.client_name,
            "batch_id": self.batch_id,
            "data_source_type": self.data_source_type,
            "total_tasks": len(self.task_list),
            "successful_updates": self.successful_updates,
            "failed_updates": self.failed_updates,
            "total_errors": len(self.errors),
            "error_format_urls": len(self.error_format_url_list),
            "failed_update_urls": len(self.failed_update_url_list),
        }
